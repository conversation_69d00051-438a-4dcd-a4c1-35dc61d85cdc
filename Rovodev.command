#!/bin/bash

# ACLI ROVODEV 快捷启动脚本
# 双击此文件即可启动 ACLI ROVODEV 程序

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 显示启动信息
echo "================================================"
echo "           ACLI ROVODEV 启动器"
echo "================================================"
echo "当前工作目录: $SCRIPT_DIR"
echo "正在启动 ACLI ROVODEV..."
echo "================================================"
echo ""

# 调用综合启动脚本
acli rovodev
