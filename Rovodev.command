#!/bin/bash

# ACLI ROVODEV 快捷启动脚本
# 双击此文件即可启动 ACLI ROVODEV 程序

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 显示启动信息
echo "================================================"
echo "           ACLI ROVODEV 启动器"
echo "================================================"
echo "当前工作目录: $SCRIPT_DIR"
echo "正在启动 ACLI ROVODEV..."
echo "================================================"
echo ""

# 检查 acli 是否可用
if ! command -v acli &> /dev/null; then
    echo "错误: acli 命令未找到"
    echo "请确保已正确安装 Atlassian CLI"
    read -p "按任意键退出..."
    exit 1
fi

# 启动函数，包含错误处理
start_rovodev() {
    echo "尝试启动 Rovo Dev CLI..."

    # 首先尝试恢复会话
    if acli rovodev --restore; then
        echo "会话恢复成功"
    else
        echo "会话恢复失败，尝试新建会话..."
        if acli rovodev; then
            echo "新会话启动成功"
        else
            echo "启动失败，请检查网络连接和配置"
            return 1
        fi
    fi
}

# 主循环，包含重试机制
MAX_RETRIES=3
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if start_rovodev; then
        break
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            echo "启动失败，5秒后重试 ($RETRY_COUNT/$MAX_RETRIES)..."
            sleep 5
        else
            echo "已达到最大重试次数，启动失败"
            echo "请检查："
            echo "1. 网络连接是否正常"
            echo "2. API 配置是否正确"
            echo "3. 环境变量是否设置正确"
            read -p "按任意键退出..."
            exit 1
        fi
    fi
done
