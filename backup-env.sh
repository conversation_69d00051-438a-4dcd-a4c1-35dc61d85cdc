#!/bin/bash

# 备用环境配置脚本
# 如果当前 API 端点有问题，可以使用此脚本切换到官方 Anthropic API

echo "================================================"
echo "           环境配置切换工具"
echo "================================================"

# 显示当前配置
echo "当前环境变量："
echo "ANTHROPIC_AUTH_TOKEN: ${ANTHROPIC_AUTH_TOKEN:0:20}..."
echo "ANTHROPIC_BASE_URL: $ANTHROPIC_BASE_URL"
echo ""

# 选择配置
echo "请选择配置："
echo "1. 使用当前配置 (anyrouter.top)"
echo "2. 切换到官方 Anthropic API"
echo "3. 显示完整配置"
echo ""

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "继续使用当前配置"
        ;;
    2)
        echo "切换到官方 Anthropic API..."
        export ANTHROPIC_BASE_URL="https://api.anthropic.com"
        echo "已切换到官方 API 端点"
        echo "注意：需要有效的 Anthropic API key"
        ;;
    3)
        echo "完整配置："
        echo "ANTHROPIC_AUTH_TOKEN: $ANTHROPIC_AUTH_TOKEN"
        echo "ANTHROPIC_BASE_URL: $ANTHROPIC_BASE_URL"
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "当前配置："
echo "ANTHROPIC_BASE_URL: $ANTHROPIC_BASE_URL"
echo ""
echo "现在可以启动 Rovo Dev CLI"
